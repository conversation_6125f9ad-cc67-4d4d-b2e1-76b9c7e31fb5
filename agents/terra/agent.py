from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmResponse, LlmRequest
from typing import Optional
from .utils.tools import think, sql, display_chart
from .utils.action_tools import (
    get_all_layers,
    toggle_layers,
    get_visible_layers,
    get_classes_breakdown,
    get_filters,
    get_sample_vectors,
    set_filters,
    update_classes,
    search_vectors,
    select_vectors,
)
from .utils.prompts import SIMPLIFIED_PROGRESS_DATA_PROMPT
from dotenv import load_dotenv
from pathlib import Path
from app.utils.logger import log_pretty

# Load environment variables from .env and .env.local file
load_dotenv()
root_dir = Path(__file__).resolve().parent.parent.parent
load_dotenv(dotenv_path=root_dir / ".env.local", override=True)


def before_model_callback(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    log_pretty("Before Model Request", llm_request)
    return None


def trim_context_callback(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    log_pretty("jhampak", callback_context.state.to_dict())
    """
    Keep only the last 5 messages. But if the first of those 5 is a function_response,
    prepend its immediate predecessor (so the function_call and its response stay together).
    """
    if llm_request.contents:
        if callback_context.state.get("initial_query"):
            callback_context.state["initial_query"] = False
            all_msgs = llm_request.contents
            n = len(all_msgs)

            # If there are 5 or more messages, grab exactly the last 5
            if n >= 5:
                last_five = all_msgs[-5:]
            else:
                last_five = all_msgs[:]  # fewer than 5 total, so just keep them all

            # Check: is the very first of these last_five a function_response?
            if last_five:
                first_of_five = last_five[0]
                if any(
                    getattr(part, "function_response", False)
                    for part in first_of_five.parts
                ):
                    # If so, and if there is a message just before those 5, prepend it
                    if n > 5:
                        # `all_msgs[-6]` is the single predecessor
                        last_five.insert(0, all_msgs[-6])

            llm_request.contents = last_five
    log_pretty("Trimmed Context", llm_request.contents)
    return None


def get_agent(metadata):
    ## metadata will have resource_type and resource_id as well to decide which agent to use if multi agent in the same module, and dynamic prompts
    config = {
        "name": "terra_agent",
        "model": LiteLlm(
            model=metadata["model"],
            parallel_tool_calls=False,
            drop_params=True,
            temperature=0,
            stream_options={"include_usage": True},
        ),
        "instruction": SIMPLIFIED_PROGRESS_DATA_PROMPT,
        "before_model_callback": trim_context_callback,
        "tools": [
            think,
            sql,
            display_chart,
            get_all_layers,
            toggle_layers,
            get_visible_layers,
            get_classes_breakdown,
            get_filters,
            get_sample_vectors,
            set_filters,
            update_classes,
            search_vectors,
            select_vectors,
        ],
    }
    if metadata.get("after_model_callback"):
        config["after_model_callback"] = metadata["after_model_callback"]
    return LlmAgent(**config)


## This is only for adk web as it looks for root_agent
root_agent = get_agent({"model": "anthropic/claude-3-7-sonnet-latest"})
