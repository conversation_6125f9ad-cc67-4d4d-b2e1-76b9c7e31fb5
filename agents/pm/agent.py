import sys
import os
from dotenv import load_dotenv

# Add the agents directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../agents-backend'))

load_dotenv(".env", override=True)
load_dotenv(".env.local", override=True)


from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from .utils.tools import think, activity_finder, sql, display_chart, summary
from .utils.prompts import get_prompt


def get_agent(payload={}):

    model = payload.get("model", "anthropic/claude-3-5-sonnet-latest")
    is_summary = payload.get("data", {}).get("type", None) == "summary"
    tools = [think, sql] if is_summary else [think, activity_finder, sql, display_chart, summary]

    config = {
        "name": "pm_agent",
        "model": LiteLlm(
            model=model,
            parallel_tool_calls=False,
            drop_params=True,
            stream_options={"include_usage": True},
        ),
        "instruction": get_prompt(payload),
        "tools": tools,
    }
    if payload.get("after_model_callback"):
        config["after_model_callback"] = payload["after_model_callback"]
    return Agent(**config)


root_agent = get_agent({})
