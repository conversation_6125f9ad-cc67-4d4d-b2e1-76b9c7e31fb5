import duckdb
from pathlib import Path


root_dir = Path(__file__).resolve().parent.parent.parent.parent


def get_connection(agent_name: str, organization_uid: str, resource_type: str, resource_id: str) -> duckdb.DuckDBPyConnection:

    """Get a connection to the DuckDB database.
    Args:
        agent_name (str): The agent name. - pm, terra, forms
        organization_uid (str): The organization UID.
        resource_type (str): The resource type. - schedule, form, activity
        resource_id (str): The resource ID. - schedule_id, form_id, activity_id
    Returns:
        duckdb.DuckDBPyConnection: The connection object.
    """

    db_path = "/".join([str(root_dir), f"data/{organization_uid}/{agent_name}/{resource_type}/{resource_id}/data.db"])
    con = duckdb.connect(database=db_path, read_only=False)
    return con
