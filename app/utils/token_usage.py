import time
from google.adk.events import Event, EventActions
from app.utils.session import SessionService
from google.adk.models import LlmResponse
from google.adk.agents.callback_context import CallbackContext

async def update_usage_metadata(item, session, session_service):
    if isinstance(item, dict):
        turn_usage_accumulator = {
            "candidates_token_count": 0,
            "prompt_token_count": 0,
            "total_token_count": 0,
            "cached_content_token_count": 0,
        }
        item_usage_data = item.get('usage_metadata')
        if isinstance(item_usage_data, dict):
            print("item usage metadata found:", item_usage_data)

            for key in turn_usage_accumulator:
                value = item_usage_data.get(key)
                if value is not None:
                    turn_usage_accumulator[key] += value

            session_state = session.state or {}
            existing_cumulative_usage = session_state.get("usageMetadata", {}) or {}

            new_cumulative_usage = {
                key: (existing_cumulative_usage.get(key, 0) or 0) + (turn_usage_accumulator.get(key, 0) or 0)
                for key in set(existing_cumulative_usage) | set(turn_usage_accumulator)
            }

            # Ensure all expected keys are present, even if they were 0 in both
            for key_template in turn_usage_accumulator:
                new_cumulative_usage.setdefault(key_template, 0)


            # 3. Update the local session_state with the new CUMULATIVE usage
            session_state["usageMetadata"] = new_cumulative_usage
            current_time = time.time()

            usage_metadata_delta_for_event = {"usageMetadata": new_cumulative_usage}
            actions_with_update = EventActions(state_delta=usage_metadata_delta_for_event)

            system_event = Event(
                invocation_id="usage_metadata_final_update", # More descriptive ID
                author="system",
                actions=actions_with_update,
                timestamp=current_time,
            )

            await session_service.append_event(session, system_event)
        
    return None


async def update_usage_after_model_callback(callback_context: CallbackContext, llm_response: LlmResponse):
    """Callback to update token usage after model response"""
    if hasattr(llm_response, "usage_metadata") and llm_response.usage_metadata:
        usage = llm_response.usage_metadata

        turn_usage_accumulator = {
            "candidates_token_count": 0,
            "prompt_token_count": 0,
            "total_token_count": 0,
            "cached_content_token_count": 0,
        }

        for key in turn_usage_accumulator:
            value = getattr(usage, key, None)
            if value is not None:
                turn_usage_accumulator[key] += value

        state = callback_context.state
        existing_cumulative_usage = state.get("usageMetadata", {}) or {}
        
        new_cumulative_usage = {
            key: (existing_cumulative_usage.get(key, 0) or 0) + (turn_usage_accumulator.get(key, 0) or 0)
            for key in set(existing_cumulative_usage) | set(turn_usage_accumulator)
        }
        
        # Ensure all expected keys are present, even if they were 0 in both
        for key_template in turn_usage_accumulator:
            new_cumulative_usage.setdefault(key_template, 0)

        callback_context.state["usageMetadata"] = new_cumulative_usage

        return None